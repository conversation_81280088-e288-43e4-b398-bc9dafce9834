<template>
  <div class="route-planner">
    <!-- 地图容器 -->
    <div id="chaoyang-map-container" class="map-container"></div>

    <!-- 优化后的控制面板 -->
    <div class="planner-panel">
      <div class="panel-header">
        <h2>北京朝阳公园导航</h2>
        <div class="panel-toggle" @click="togglePanel">
          <span class="toggle-icon">▶</span>
        </div>
      </div>

      <div class="panel-content">
        <!-- 地点选择区域 -->
        <div class="location-selector">
          <div class="selector-title">选择地点：</div>

          <!-- 起点选择 -->
          <div class="location-item">
            <div class="location-label">起点：</div>
            <div class="location-input">
              <div class="autocomplete-container">
                <input
                  type="text"
                  v-model="startSearchText"
                  @input="filterStartLocations"
                  @focus="showStartSuggestions = true"
                  @blur="handleStartBlur"
                  placeholder="请输入起点"
                  class="location-input-field"
                />
                <div v-if="showStartSuggestions && filteredStartLocations.length > 0" class="suggestions-container">
                  <div
                    v-for="location in filteredStartLocations"
                    :key="'start-'+location.id"
                    class="suggestion-item"
                    @mousedown="selectStartLocation(location)"
                  >
                    {{ location.label }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 途径点选择 -->
          <div class="waypoints-container">
            <div class="waypoints-header">
              <div class="waypoints-title">途径点：</div>
              <button @click="addWaypoint" class="add-waypoint-btn">
                <span>+</span> 添加途径点
              </button>
            </div>

            <div v-if="waypoints.length === 0" class="no-waypoints-message">
              未添加途径点，可点击上方按钮添加
            </div>

            <div v-for="(waypoint, index) in waypoints" :key="'waypoint-'+index" class="waypoint-item">
              <div class="waypoint-index">{{ index + 1 }}</div>
              <div class="waypoint-input">
                <div class="autocomplete-container">
                  <input
                    type="text"
                    v-model="waypoint.searchText"
                    @input="() => filterWaypointLocations(index)"
                    @focus="waypoint.showSuggestions = true"
                    @blur="() => handleWaypointBlur(index)"
                    placeholder="请输入途径点"
                    class="location-input-field"
                  />
                  <div v-if="waypoint.showSuggestions && waypoint.filteredLocations.length > 0" class="suggestions-container">
                    <div
                      v-for="location in waypoint.filteredLocations"
                      :key="'waypoint-'+index+'-'+location.id"
                      class="suggestion-item"
                      @mousedown="() => selectWaypointLocation(index, location)"
                    >
                      {{ location.label }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="waypoint-actions">
                <button @click="() => removeWaypoint(index)" class="remove-waypoint-btn">
                  <span>×</span>
                </button>
              </div>
            </div>
          </div>

          <!-- 终点选择 -->
          <div class="location-item">
            <div class="location-label">终点：</div>
            <div class="location-input">
              <div class="autocomplete-container">
                <input
                  type="text"
                  v-model="endSearchText"
                  @input="filterEndLocations"
                  @focus="showEndSuggestions = true"
                  @blur="handleEndBlur"
                  placeholder="请输入终点"
                  class="location-input-field"
                />
                <div v-if="showEndSuggestions && filteredEndLocations.length > 0" class="suggestions-container">
                  <div
                    v-for="location in filteredEndLocations"
                    :key="'end-'+location.id"
                    class="suggestion-item"
                    @mousedown="selectEndLocation(location)"
                  >
                    {{ location.label }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="selector-note">
            或者直接在地图上点击选择位置
          </div>

          <!-- 路径规划策略选择 -->
          <div class="strategy-selector">
            <div class="strategy-title">规划策略：</div>
            <div class="strategy-options">
              <div
                class="strategy-option"
                :class="{ active: selectedStrategy === 0 }"
                @click="selectStrategy(0)"
              >
                <span class="strategy-icon">🚗</span>
                <span>最短距离</span>
              </div>
              <div
                class="strategy-option"
                :class="{ active: selectedStrategy === 1 }"
                @click="selectStrategy(1)"
              >
                <span class="strategy-icon">⏱️</span>
                <span>最短时间</span>
              </div>
              <div
                class="strategy-option"
                :class="{ active: selectedStrategy === 3 }"
                @click="selectStrategy(3)"
              >
                <span class="strategy-icon">🚀</span>
                <span>智能出行</span>
              </div>
            </div>
          </div>
        </div>

        <div class="marker-controls">
          <div class="marker-mode">
            <div class="mode-title">地图标记模式：</div>
            <div class="mode-options">
              <div
                class="mode-option"
                :class="{ active: currentMode === 'start' }"
                @click="setMode('start')"
              >
                起点
              </div>
              <div
                class="mode-option"
                :class="{ active: currentMode === 'waypoint' }"
                @click="setMode('waypoint')"
              >
                途径点
              </div>
              <div
                class="mode-option"
                :class="{ active: currentMode === 'end' }"
                @click="setMode('end')"
              >
                终点
              </div>
            </div>
          </div>
          <div class="marker-instructions">
            {{ getInstructions() }}
          </div>
        </div>

        <div class="marker-info">
          <div class="marker-item" v-if="startMarker">
            <div class="marker-label">起点：</div>
            <div class="marker-value">{{ getLocationDisplayName(startLocationName) || '地图选择的位置' }}</div>
          </div>

          <!-- 途径点信息 -->
          <div v-for="(waypoint, index) in visibleWaypoints" :key="'waypoint-info-'+index" class="marker-item">
            <div class="marker-label">途径点{{ getWaypointIndex(waypoint) + 1 }}：</div>
            <div class="marker-value">{{ getLocationDisplayName(waypoint.locationName) || '地图选择的位置' }}</div>
          </div>

          <div class="marker-item" v-if="endMarker">
            <div class="marker-label">终点：</div>
            <div class="marker-value">{{ getLocationDisplayName(endLocationName) || '地图选择的位置' }}</div>
          </div>
        </div>

        <!-- 交通方式选择 -->
        <div class="transport-mode">
          <div class="mode-title">交通方式：</div>
          <div class="transport-options">
            <div
              v-for="mode in transportModes"
              :key="mode.value"
              class="transport-option"
              :class="{
                active: selectedTransportMode === mode.value,
                disabled: selectedStrategy === 3 && mode.value !== 'driving'
              }"
              @click="selectTransportMode(mode.value)"
            >
              <span class="mode-icon">{{ mode.icon }}</span>
              <span>{{ mode.label }}</span>
            </div>
          </div>
        </div>

        <!-- 路线信息 -->
        <div class="route-info">
          <div class="route-stats">
            <div class="stat-item">
              <span class="stat-label">距离：</span>
              <span class="stat-value">{{ routeDistance }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">预计时间：</span>
              <span class="stat-value">{{ routeDuration }}</span>
            </div>
          </div>

          <!-- 路线详情 -->
          <div v-if="routeSteps.length > 0" class="route-steps">
            <div class="steps-title">路线详情：</div>
            <div class="steps-list">
              <div v-for="(step, index) in routeSteps" :key="index" class="step-item">
                <div class="step-number">{{ index + 1 }}</div>
                <div class="step-content">{{ step }}</div>
              </div>
            </div>
          </div>

          <div class="action-buttons">
            <button @click="calculateRoute" class="action-btn calculate-btn" :disabled="!canCalculate">
              规划路线
            </button>
            <button @click="startNavigation" class="action-btn navigate-btn" :disabled="!canCalculate">
              开始导航
            </button>
            <button @click="clearMarkers" class="action-btn clear-btn">
              清除标记
            </button>
          </div>

          <!-- 功能说明 -->
          <div class="feature-notice">
            <div class="notice-title">🚧 功能说明</div>
            <div class="notice-content">
              朝阳公园导航功能正在开发中，当前仅提供地图标记和基础路线规划功能。
              完整的后端路径算法将在后续版本中提供。
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, computed } from 'vue'
import AMapLoader from '@amap/amap-jsapi-loader'

// 创建地图实例和标记点的引用
const mapInstance = ref(null)
const startMarker = ref(null)
const endMarker = ref(null)
const polyline = ref(null)
const currentMode = ref('start') // 'start', 'waypoint' 或 'end'
const routeDistance = ref('0 米')
const routeDuration = ref('0 分钟')
const routeSteps = ref([])
const selectedTransportMode = ref('driving')
const hasRoute = ref(false)

// 地点选择相关
const locations = ref([])
const selectedStartId = ref('')
const selectedEndId = ref('')
const startLocationName = ref('')
const endLocationName = ref('')

// 途径点相关
const waypoints = ref([])
const currentWaypointIndex = ref(0) // 当前选择的途径点索引

// 自动完成相关
const startSearchText = ref('')
const endSearchText = ref('')
const filteredStartLocations = ref([])
const filteredEndLocations = ref([])
const showStartSuggestions = ref(false)
const showEndSuggestions = ref(false)

// 路径规划策略
const selectedStrategy = ref(0) // 0: 最短距离, 1: 最短时间, 3: 智能出行

// 面板控制
const isPanelCollapsed = ref(false)

// 朝阳公园地点数据（示例数据）
const chaoyangParkLocations = [
  { id: 1, label: '南门', x: 116.473, y: 39.9288 },
  { id: 2, label: '北门', x: 116.4735, y: 39.9355 },
  { id: 3, label: '东门', x: 116.4785, y: 39.9315 },
  { id: 4, label: '西门', x: 116.4685, y: 39.9315 },
  { id: 5, label: '湖心岛', x: 116.4735, y: 39.9315 },
  { id: 6, label: '儿童游乐场', x: 116.4745, y: 39.9325 },
  { id: 7, label: '音乐喷泉', x: 116.4725, y: 39.9305 },
  { id: 8, label: '樱花园', x: 116.4755, y: 39.9335 },
  { id: 9, label: '荷花池', x: 116.4715, y: 39.9295 },
  { id: 10, label: '健身广场', x: 116.4765, y: 39.9345 }
]

// 交通方式选项
const transportModes = [
  { value: 'driving', label: '不限', icon: '🚗' },
  { value: 'walking', label: '步行', icon: '🚶' },
  { value: 'riding', label: '骑行', icon: '🚴' }
]

// 计算属性：是否可以计算路线
const canCalculate = computed(() => {
  return startMarker.value && endMarker.value
})

// 计算属性：可见的途径点（有标记的）
const visibleWaypoints = computed(() => {
  return waypoints.value.filter(waypoint => waypoint.marker)
})

// 获取途径点在原数组中的索引
const getWaypointIndex = (waypoint) => {
  return waypoints.value.findIndex(wp => wp === waypoint)
}

// 提取位置显示名称（只显示地点名称）
const getLocationDisplayName = (fullLocationName) => {
  if (!fullLocationName) return ''
  return fullLocationName
}

// 初始化地点数据
const initializeLocations = () => {
  locations.value = chaoyangParkLocations
}

// 添加途径点
const addWaypoint = () => {
  waypoints.value.push({
    marker: null,
    vertexId: '',
    locationName: '',
    searchText: '',
    showSuggestions: false,
    filteredLocations: []
  })

  // 自动切换到途径点模式
  currentMode.value = 'waypoint'
  currentWaypointIndex.value = waypoints.value.length - 1
}

// 移除途径点
const removeWaypoint = (index) => {
  // 如果有标记，先从地图上移除
  if (waypoints.value[index].marker) {
    mapInstance.value.remove(waypoints.value[index].marker)
  }

  // 从数组中移除
  waypoints.value.splice(index, 1)

  // 如果移除后没有途径点，切换到起点或终点模式
  if (waypoints.value.length === 0) {
    if (!startMarker.value) {
      currentMode.value = 'start'
    } else if (!endMarker.value) {
      currentMode.value = 'end'
    }
  } else if (currentWaypointIndex.value >= waypoints.value.length) {
    // 如果当前索引超出范围，重置为最后一个
    currentWaypointIndex.value = waypoints.value.length - 1
  }

  // 如果已有路线，清除
  if (polyline.value) {
    mapInstance.value.remove(polyline.value)
    polyline.value = null
    routeDistance.value = '0 米'
    routeSteps.value = []
    hasRoute.value = false
  }
}

// 过滤起点位置
const filterStartLocations = () => {
  if (!startSearchText.value.trim()) {
    filteredStartLocations.value = []
    return
  }

  const searchText = startSearchText.value.toLowerCase().trim()
  filteredStartLocations.value = locations.value.filter(location =>
    location.label && location.label.toLowerCase().includes(searchText)
  ).slice(0, 10) // 限制最多显示10个结果
}

// 过滤终点位置
const filterEndLocations = () => {
  if (!endSearchText.value.trim()) {
    filteredEndLocations.value = []
    return
  }

  const searchText = endSearchText.value.toLowerCase().trim()
  filteredEndLocations.value = locations.value.filter(location =>
    location.label && location.label.toLowerCase().includes(searchText)
  ).slice(0, 10) // 限制最多显示10个结果
}

// 过滤途径点位置
const filterWaypointLocations = (index) => {
  const waypoint = waypoints.value[index]
  if (!waypoint.searchText.trim()) {
    waypoint.filteredLocations = []
    return
  }

  const searchText = waypoint.searchText.toLowerCase().trim()
  waypoint.filteredLocations = locations.value.filter(location =>
    location.label && location.label.toLowerCase().includes(searchText)
  ).slice(0, 10) // 限制最多显示10个结果
}

// 选择起点位置
const selectStartLocation = (location) => {
  selectedStartId.value = location.id
  startSearchText.value = location.label
  showStartSuggestions.value = false

  // 在地图上标记位置
  const position = new window.AMap.LngLat(location.x, location.y)
  addStartMarker(position)

  startLocationName.value = location.label
}

// 选择终点位置
const selectEndLocation = (location) => {
  selectedEndId.value = location.id
  endSearchText.value = location.label
  showEndSuggestions.value = false

  // 在地图上标记位置
  const position = new window.AMap.LngLat(location.x, location.y)
  addEndMarker(position)

  endLocationName.value = location.label
}

// 选择途径点位置
const selectWaypointLocation = (index, location) => {
  const waypoint = waypoints.value[index]
  waypoint.vertexId = location.id
  waypoint.searchText = location.label
  waypoint.showSuggestions = false

  // 在地图上标记位置
  const position = new window.AMap.LngLat(location.x, location.y)
  addWaypointMarker(position, index)

  waypoint.locationName = location.label
}

// 处理起点输入框失焦事件
const handleStartBlur = () => {
  setTimeout(() => {
    showStartSuggestions.value = false

    // 如果没有选择任何地点但有输入文本，尝试匹配最接近的地点
    if (!selectedStartId.value && startSearchText.value.trim()) {
      const matchedLocation = locations.value.find(loc =>
        loc.label && loc.label.toLowerCase() === startSearchText.value.toLowerCase().trim()
      )

      if (matchedLocation) {
        selectStartLocation(matchedLocation)
      }
    }
  }, 200)
}

// 处理终点输入框失焦事件
const handleEndBlur = () => {
  setTimeout(() => {
    showEndSuggestions.value = false

    // 如果没有选择任何地点但有输入文本，尝试匹配最接近的地点
    if (!selectedEndId.value && endSearchText.value.trim()) {
      const matchedLocation = locations.value.find(loc =>
        loc.label && loc.label.toLowerCase() === endSearchText.value.toLowerCase().trim()
      )

      if (matchedLocation) {
        selectEndLocation(matchedLocation)
      }
    }
  }, 200)
}

// 处理途径点输入框失焦事件
const handleWaypointBlur = (index) => {
  setTimeout(() => {
    const waypoint = waypoints.value[index]
    waypoint.showSuggestions = false

    // 如果没有选择任何地点但有输入文本，尝试匹配最接近的地点
    if (!waypoint.vertexId && waypoint.searchText.trim()) {
      const matchedLocation = locations.value.find(loc =>
        loc.label && loc.label.toLowerCase() === waypoint.searchText.toLowerCase().trim()
      )

      if (matchedLocation) {
        selectWaypointLocation(index, matchedLocation)
      }
    }
  }, 200)
}

// 选择策略
const selectStrategy = (strategy) => {
  selectedStrategy.value = strategy

  // 如果选择智能出行，自动设置为不限模式
  if (strategy === 3) {
    selectedTransportMode.value = 'driving'
  }
}

// 选择交通方式
const selectTransportMode = (mode) => {
  // 如果是智能出行策略，只允许选择不限模式
  if (selectedStrategy.value === 3 && mode !== 'driving') {
    return
  }

  selectedTransportMode.value = mode
}

// 设置标记模式
const setMode = (mode) => {
  currentMode.value = mode

  // 如果切换到途径点模式，确保有途径点可选择
  if (mode === 'waypoint' && waypoints.value.length === 0) {
    addWaypoint()
  }
}

// 获取操作指引
const getInstructions = () => {
  switch (currentMode.value) {
    case 'start':
      return '点击地图选择起点位置'
    case 'waypoint':
      return '点击地图选择途径点位置'
    case 'end':
      return '点击地图选择终点位置'
    default:
      return '请选择标记模式'
  }
}

// 面板切换
const togglePanel = () => {
  isPanelCollapsed.value = !isPanelCollapsed.value
  const panel = document.querySelector('.planner-panel')
  const toggleIcon = document.querySelector('.toggle-icon')

  if (isPanelCollapsed.value) {
    panel.classList.add('collapsed')
    toggleIcon.textContent = '◀'
  } else {
    panel.classList.remove('collapsed')
    toggleIcon.textContent = '▶'
  }
}

// 添加起点标记
const addStartMarker = (position) => {
  // 如果已有起点标记，先移除
  if (startMarker.value) {
    mapInstance.value.remove(startMarker.value)
  }

  // 创建新的起点标记
  startMarker.value = new window.AMap.Marker({
    position: position,
    icon: new window.AMap.Icon({
      size: new window.AMap.Size(32, 32),
      image: 'data:image/svg+xml;base64,' + btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
          <circle cx="16" cy="16" r="12" fill="#00ff00" stroke="#fff" stroke-width="2"/>
          <text x="16" y="20" text-anchor="middle" fill="#fff" font-size="10" font-weight="bold">起</text>
        </svg>
      `),
      imageSize: new window.AMap.Size(32, 32)
    }),
    title: '起点',
    label: {
      content: '起点',
      offset: new window.AMap.Pixel(0, -40),
      direction: 'top'
    }
  })

  mapInstance.value.add(startMarker.value)
}

// 添加终点标记
const addEndMarker = (position) => {
  // 如果已有终点标记，先移除
  if (endMarker.value) {
    mapInstance.value.remove(endMarker.value)
  }

  // 创建新的终点标记
  endMarker.value = new window.AMap.Marker({
    position: position,
    icon: new window.AMap.Icon({
      size: new window.AMap.Size(32, 32),
      image: 'data:image/svg+xml;base64,' + btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
          <circle cx="16" cy="16" r="12" fill="#ff0000" stroke="#fff" stroke-width="2"/>
          <text x="16" y="20" text-anchor="middle" fill="#fff" font-size="10" font-weight="bold">终</text>
        </svg>
      `),
      imageSize: new window.AMap.Size(32, 32)
    }),
    title: '终点',
    label: {
      content: '终点',
      offset: new window.AMap.Pixel(0, -40),
      direction: 'top'
    }
  })

  mapInstance.value.add(endMarker.value)
}

// 添加途径点标记
const addWaypointMarker = (position, index) => {
  const waypoint = waypoints.value[index]

  // 如果已有标记，先移除
  if (waypoint.marker) {
    mapInstance.value.remove(waypoint.marker)
  }

  // 创建新的途径点标记
  waypoint.marker = new window.AMap.Marker({
    position: position,
    icon: new window.AMap.Icon({
      size: new window.AMap.Size(32, 32),
      image: 'data:image/svg+xml;base64,' + btoa(`
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
          <circle cx="16" cy="16" r="12" fill="#0066ff" stroke="#fff" stroke-width="2"/>
          <text x="16" y="20" text-anchor="middle" fill="#fff" font-size="10" font-weight="bold">${index + 1}</text>
        </svg>
      `),
      imageSize: new window.AMap.Size(32, 32)
    }),
    title: `途径点${index + 1}`,
    label: {
      content: `途径点${index + 1}`,
      offset: new window.AMap.Pixel(0, -40),
      direction: 'top'
    }
  })

  mapInstance.value.add(waypoint.marker)
}

// 清除所有标记
const clearMarkers = () => {
  // 清除起点标记
  if (startMarker.value) {
    mapInstance.value.remove(startMarker.value)
    startMarker.value = null
    startLocationName.value = ''
    selectedStartId.value = ''
    startSearchText.value = ''
  }

  // 清除终点标记
  if (endMarker.value) {
    mapInstance.value.remove(endMarker.value)
    endMarker.value = null
    endLocationName.value = ''
    selectedEndId.value = ''
    endSearchText.value = ''
  }

  // 清除途径点标记
  waypoints.value.forEach(waypoint => {
    if (waypoint.marker) {
      mapInstance.value.remove(waypoint.marker)
    }
  })
  waypoints.value = []

  // 清除路线
  if (polyline.value) {
    mapInstance.value.remove(polyline.value)
    polyline.value = null
  }

  // 重置路线信息
  routeDistance.value = '0 米'
  routeDuration.value = '0 分钟'
  routeSteps.value = []
  hasRoute.value = false

  // 重置模式
  currentMode.value = 'start'
}

// 计算路线（示例功能）
const calculateRoute = () => {
  if (!startMarker.value || !endMarker.value) {
    alert('请先选择起点和终点')
    return
  }

  // 这里是示例路线计算，实际应该调用后端API
  const startPos = startMarker.value.getPosition()
  const endPos = endMarker.value.getPosition()

  // 计算直线距离
  const distance = window.AMap.GeometryUtil.distance(startPos, endPos)
  routeDistance.value = `${(distance / 1000).toFixed(2)} 公里`

  // 估算时间（基于步行速度5km/h）
  const walkingTime = (distance / 1000) / 5 * 60 // 分钟
  routeDuration.value = `${Math.ceil(walkingTime)} 分钟`

  // 创建简单的直线路径
  const path = [startPos, endPos]

  // 如果有途径点，加入路径
  waypoints.value.forEach(waypoint => {
    if (waypoint.marker) {
      path.splice(-1, 0, waypoint.marker.getPosition())
    }
  })

  // 绘制路线
  if (polyline.value) {
    mapInstance.value.remove(polyline.value)
  }

  polyline.value = new window.AMap.Polyline({
    path: path,
    strokeColor: '#3366FF',
    strokeWeight: 4,
    strokeOpacity: 0.8
  })

  mapInstance.value.add(polyline.value)

  // 调整地图视野
  mapInstance.value.setFitView([polyline.value])

  // 设置路线步骤
  routeSteps.value = [
    `从 ${getLocationDisplayName(startLocationName.value) || '起点'} 出发`,
    ...waypoints.value.filter(wp => wp.marker).map((wp, index) =>
      `经过 ${getLocationDisplayName(wp.locationName) || `途径点${index + 1}`}`
    ),
    `到达 ${getLocationDisplayName(endLocationName.value) || '终点'}`
  ]

  hasRoute.value = true
}

// 开始导航（示例功能）
const startNavigation = () => {
  if (!hasRoute.value) {
    alert('请先规划路线')
    return
  }

  alert('导航功能正在开发中，敬请期待！')
}

// 地图点击事件处理
const handleMapClick = (e) => {
  const position = e.lnglat

  switch (currentMode.value) {
    case 'start':
      addStartMarker(position)
      startLocationName.value = `地图选择的位置 (${position.lng.toFixed(6)}, ${position.lat.toFixed(6)})`
      break
    case 'waypoint': {
      if (waypoints.value.length === 0) {
        addWaypoint()
      }
      const currentWaypoint = waypoints.value[currentWaypointIndex.value]
      if (currentWaypoint) {
        addWaypointMarker(position, currentWaypointIndex.value)
        currentWaypoint.locationName = `地图选择的位置 (${position.lng.toFixed(6)}, ${position.lat.toFixed(6)})`
      }
      break
    }
    case 'end':
      addEndMarker(position)
      endLocationName.value = `地图选择的位置 (${position.lng.toFixed(6)}, ${position.lat.toFixed(6)})`
      break
  }
}

// 初始化地图
const initMap = async () => {
  try {
    const AMap = await AMapLoader.load({
      key: '4b5b8b8b8b8b8b8b8b8b8b8b8b8b8b8b', // 请替换为您的高德地图API密钥
      version: '2.0',
      plugins: ['AMap.GeometryUtil']
    })

    // 创建地图实例
    mapInstance.value = new AMap.Map('chaoyang-map-container', {
      zoom: 16,
      center: [116.4735, 39.9315], // 朝阳公园中心位置
      mapStyle: 'amap://styles/normal'
    })

    // 添加地图点击事件
    mapInstance.value.on('click', handleMapClick)

    // 初始化地点数据
    initializeLocations()

    console.log('朝阳公园地图初始化成功')
  } catch (error) {
    console.error('地图初始化失败:', error)
  }
}

// 组件挂载时初始化地图
onMounted(() => {
  initMap()
})

// 组件卸载时清理资源
onBeforeUnmount(() => {
  if (mapInstance.value) {
    mapInstance.value.destroy()
  }
})
</script>

<style scoped>
.route-planner {
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
}

.map-container {
  flex: 1;
  height: 100%;
  background-color: #f0f0f0;
}

.planner-panel {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 380px;
  max-height: calc(100vh - 40px);
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transition: transform 0.3s ease;
  overflow: hidden;
}

.planner-panel.collapsed {
  transform: translateX(340px);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.panel-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.panel-toggle {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.2);
  transition: background 0.2s;
}

.panel-toggle:hover {
  background: rgba(255, 255, 255, 0.3);
}

.toggle-icon {
  font-size: 14px;
  font-weight: bold;
}

.panel-content {
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  padding: 20px;
}

/* 地点选择区域样式 */
.location-selector {
  margin-bottom: 20px;
}

.selector-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.location-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
}

.location-label {
  min-width: 50px;
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.location-input {
  flex: 1;
}

.autocomplete-container {
  position: relative;
}

.location-input-field {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
  box-sizing: border-box;
}

.location-input-field:focus {
  outline: none;
  border-color: #667eea;
}

.suggestions-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e1e5e9;
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1001;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-item {
  padding: 10px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  transition: background-color 0.2s;
}

.suggestion-item:hover {
  background-color: #f8f9fa;
}

.suggestion-item:last-child {
  border-bottom: none;
}

/* 途径点样式 */
.waypoints-container {
  margin: 16px 0;
}

.waypoints-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.waypoints-title {
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.add-waypoint-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.2s;
}

.add-waypoint-btn:hover {
  background: #218838;
}

.no-waypoints-message {
  color: #6c757d;
  font-size: 13px;
  font-style: italic;
  text-align: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.waypoint-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

.waypoint-index {
  min-width: 24px;
  height: 24px;
  background: #0066ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.waypoint-input {
  flex: 1;
}

.waypoint-actions {
  display: flex;
  gap: 4px;
}

.remove-waypoint-btn {
  background: #dc3545;
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s;
}

.remove-waypoint-btn:hover {
  background: #c82333;
}

.selector-note {
  font-size: 12px;
  color: #6c757d;
  text-align: center;
  margin: 12px 0;
  font-style: italic;
}

/* 策略选择样式 */
.strategy-selector {
  margin: 16px 0;
}

.strategy-title {
  font-weight: 500;
  color: #555;
  font-size: 14px;
  margin-bottom: 8px;
}

.strategy-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.strategy-option {
  flex: 1;
  min-width: 100px;
  padding: 10px 8px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
  font-size: 13px;
}

.strategy-option:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.strategy-option.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.strategy-icon {
  display: block;
  font-size: 16px;
  margin-bottom: 4px;
}

/* 标记控制样式 */
.marker-controls {
  margin: 20px 0;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.marker-mode {
  margin-bottom: 12px;
}

.mode-title {
  font-weight: 500;
  color: #555;
  font-size: 14px;
  margin-bottom: 8px;
}

.mode-options {
  display: flex;
  gap: 8px;
}

.mode-option {
  flex: 1;
  padding: 8px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
  font-size: 13px;
}

.mode-option:hover {
  border-color: #667eea;
  background: #f8f9ff;
}

.mode-option.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.marker-instructions {
  font-size: 13px;
  color: #6c757d;
  text-align: center;
  font-style: italic;
}

/* 标记信息样式 */
.marker-info {
  margin: 16px 0;
  padding: 12px;
  background: #f0f8ff;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.marker-item {
  display: flex;
  margin-bottom: 6px;
  font-size: 13px;
}

.marker-item:last-child {
  margin-bottom: 0;
}

.marker-label {
  min-width: 60px;
  font-weight: 500;
  color: #555;
}

.marker-value {
  flex: 1;
  color: #333;
}

/* 交通方式样式 */
.transport-mode {
  margin: 16px 0;
}

.transport-options {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.transport-option {
  flex: 1;
  min-width: 80px;
  padding: 10px 8px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s;
  background: white;
  font-size: 13px;
}

.transport-option:hover:not(.disabled) {
  border-color: #667eea;
  background: #f8f9ff;
}

.transport-option.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.transport-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f5f5f5;
}

.mode-icon {
  display: block;
  font-size: 16px;
  margin-bottom: 4px;
}

/* 路线信息样式 */
.route-info {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e1e5e9;
}

.route-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-item {
  flex: 1;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 4px;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 路线步骤样式 */
.route-steps {
  margin: 16px 0;
}

.steps-title {
  font-weight: 500;
  color: #555;
  font-size: 14px;
  margin-bottom: 8px;
}

.steps-list {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.step-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  font-size: 13px;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-number {
  min-width: 20px;
  height: 20px;
  background: #667eea;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 11px;
  font-weight: bold;
  margin-right: 8px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
  line-height: 1.4;
  color: #333;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  margin: 16px 0;
}

.action-btn {
  flex: 1;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.calculate-btn {
  background: #28a745;
  color: white;
}

.calculate-btn:hover:not(:disabled) {
  background: #218838;
}

.navigate-btn {
  background: #007bff;
  color: white;
}

.navigate-btn:hover:not(:disabled) {
  background: #0056b3;
}

.clear-btn {
  background: #6c757d;
  color: white;
}

.clear-btn:hover {
  background: #545b62;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 功能说明样式 */
.feature-notice {
  margin-top: 20px;
  padding: 16px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
}

.notice-title {
  font-weight: 600;
  color: #856404;
  margin-bottom: 8px;
  font-size: 14px;
}

.notice-content {
  font-size: 13px;
  color: #856404;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .planner-panel {
    width: calc(100% - 40px);
    max-width: 400px;
  }

  .strategy-options {
    flex-direction: column;
  }

  .strategy-option {
    min-width: auto;
  }

  .transport-options {
    flex-direction: column;
  }

  .transport-option {
    min-width: auto;
  }

  .route-stats {
    flex-direction: column;
    gap: 8px;
  }

  .action-buttons {
    flex-direction: column;
  }
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar,
.steps-list::-webkit-scrollbar,
.suggestions-container::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track,
.steps-list::-webkit-scrollbar-track,
.suggestions-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb,
.steps-list::-webkit-scrollbar-thumb,
.suggestions-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover,
.steps-list::-webkit-scrollbar-thumb:hover,
.suggestions-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
